"use client"
import React from 'react';
import { MapPin, Star, Users, Award, Mountain, Camera } from 'lucide-react';

const OfficialGuideSection = () => {
  return (
    <section className="relative py-24 overflow-hidden">
      {/* Enhanced Background with Layered Effects */}
      <div className="absolute inset-0">
        {/* Base Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-emerald-900 to-teal-900"></div>

        {/* CSS Mountain Silhouette Background */}
        <div
          className="absolute inset-0 opacity-60"
          style={{
            background: `
              linear-gradient(180deg, transparent 0%, transparent 40%, #065f46 40%, #047857 60%, #064e3b 100%),
              linear-gradient(45deg, transparent 0%, transparent 30%, #065f46 30%, #047857 50%, transparent 50%, transparent 70%, #064e3b 70%, #065f46 100%),
              linear-gradient(-45deg, transparent 0%, transparent 20%, #047857 20%, #065f46 40%, transparent 40%, transparent 80%, #064e3b 80%, #047857 100%)
            `,
            backgroundSize: '100% 100%, 200px 200px, 150px 150px',
            backgroundPosition: 'center bottom, 0 0, 100px 50px'
          }}
        />

        {/* Local Mountain Background */}
        <div
          className="absolute inset-0 opacity-80 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('/mountain-bg.svg')`
          }}
        />

        {/* Gradient Overlays for Depth */}
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/80 via-transparent to-emerald-900/20" />
        <div className="absolute inset-0 bg-gradient-to-br from-lime-400/5 via-green-500/10 to-emerald-600/15" />

        {/* Subtle Texture Pattern */}
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20s-20-8.954-20-20 8.954-20 20-20 20 8.954 20 20zm-20-18c-9.941 0-18 8.059-18 18s8.059 18 18 18 18-8.059 18-18-8.059-18-18-18z'/%3E%3C/g%3E%3C/svg%3E")`,
            animation: 'pattern-shift 60s linear infinite'
          }}
        />
      </div>

      {/* Animated Icons with Staggered Effects */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-16 left-16 w-16 h-16 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-float">
          <Mountain className="w-8 h-8 text-emerald-600" />
        </div>
        
        <div className="absolute top-24 right-24 w-14 h-14 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-pulse-slow">
          <Star className="w-7 h-7 text-amber-400 fill-current" />
        </div>
        
        <div className="absolute bottom-24 left-32 w-12 h-12 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-float-delayed">
          <Camera className="w-6 h-6 text-blue-500" />
        </div>
        
        <div className="absolute bottom-32 right-16 w-18 h-18 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-pulse-delayed">
          <Award className="w-9 h-9 text-orange-500" />
        </div>
        
        <div className="absolute top-1/2 left-8 w-14 h-14 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-float-slow">
          <Users className="w-7 h-7 text-purple-500" />
        </div>
        
        <div className="absolute top-1/3 right-8 w-16 h-16 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-pulse-slower">
          <MapPin className="w-8 h-8 text-rose-500" />
        </div>
      </div>

      {/* Content Container */}
      <div className="max-w-[80vw] mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center text-white">
          {/* Premium Badge */}
          <div className="inline-flex items-center bg-white/20 backdrop-blur-lg rounded-full px-6 py-3 mb-10 border border-white/10 shadow-lg">
            <Award className="w-6 h-6 mr-2 text-white" />
            <span className="text-base font-semibold tracking-wide">Official Guide Experience</span>
          </div>

          {/* Headline */}
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 leading-tight tracking-tight">
            Discover Ourika with
            <span className="block bg-gradient-to-r from-lime-200 to-emerald-200 bg-clip-text text-transparent mt-2">
              Certified Local Guides
            </span>
          </h2>

          {/* Description */}
          <p className="text-xl mb-12 text-white/90 leading-relaxed max-w-3xl mx-auto font-light">
            Experience the authentic beauty of Ourika Valley with our certified local guides.
            From hidden waterfalls to traditional Berber villages, unlock exclusive access to
            the most breathtaking locations that only locals know.
          </p>

          {/* Features Grid */}
          <div className="grid md:grid-cols-3 gap-10 mb-14">
            <div className="flex flex-col items-center group">
              <div className="w-18 h-18 bg-white/95 rounded-full flex items-center justify-center mb-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Star className="w-8 h-8 text-emerald-600 fill-current" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-3">Expert Knowledge</h3>
              <p className="text-white/80 text-center text-base leading-relaxed">Deep understanding of local culture, history, and traditions</p>
            </div>

            <div className="flex flex-col items-center group">
              <div className="w-18 h-18 bg-white/95 rounded-full flex items-center justify-center mb-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Mountain className="w-8 h-8 text-emerald-600" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-3">Hidden Trails</h3>
              <p className="text-white/80 text-center text-base leading-relaxed">Access to secret spots and breathtaking viewpoints</p>
            </div>

            <div className="flex flex-col items-center group">
              <div className="w-18 h-18 bg-white/95 rounded-full flex items-center justify-center mb-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Users className="w-8 h-8 text-emerald-600" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-3">Personal Touch</h3>
              <p className="text-white/80 text-center text-base leading-relaxed">Small groups for an intimate and personalized experience</p>
            </div>
          </div>

          {/* CTA Button */}
          <button className="bg-white hover:bg-gray-50 text-gray-900 px-10 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-emerald-700">
            Book Your Guide Today
          </button>
        </div>
      </div>

      {/* Custom Animation Styles */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-10px); }
        }
        @keyframes pattern-shift {
          0% { background-position: 0 0; }
          100% { background-position: 50px 50px; }
        }
        .animate-float { animation: float 6s ease-in-out infinite; }
        .animate-float-delayed { animation: float 7s ease-in-out infinite 1s; }
        .animate-float-slow { animation: float 8s ease-in-out infinite; }
        .animate-pulse-slow { animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        .animate-pulse-delayed { animation: pulse 5s cubic-bezier(0.4, 0, 0.6, 1) infinite 1s; }
        .animate-pulse-slower { animation: pulse 6s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        .animate-pattern-shift { animation: pattern-shift 30s linear infinite; }
      `}</style>
    </section>
  );
};

export default OfficialGuideSection;